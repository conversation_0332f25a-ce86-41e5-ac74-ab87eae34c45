import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Checkbox } from "../ui/checkbox";
import { FieldTitle } from "./FieldTitle";
import { LinkRenderer } from "../custom/linkRender";

interface RadioButtonProps {
  register?: any;
  label?: string;
  fieldItem?: any;
  isMandatory?: boolean;
  selectedValue?: boolean;
  handleChange?: any;
  disabled?: boolean;
  errorMessage?: string;
  markdownText?: string;
}

export function CheckBox(props: RadioButtonProps) {
  const {
    register,
    label,
    fieldItem,
    isMandatory,
    selectedValue,
    handleChange,
    disabled,
    errorMessage,
    markdownText,
  } = props;

  console.log(markdownText);

  const checkboxId = `checkbox-${fieldItem?.name || label || "default"}`;

  // Handle keyboard events for the checkbox
  const handleKeyDown = (event: React.KeyboardEvent) => {
    console.log(
      "Checkbox key pressed:",
      event.key,
      "Current value:",
      selectedValue
    );

    // Handle Enter key specifically (Space is handled by Radix UI automatically)
    if (event.key === "Enter") {
      event.preventDefault();
      event.stopPropagation();
      console.log("Enter key pressed - toggling checkbox");
      if (!disabled) {
        const newValue = !selectedValue;
        console.log("Changing checkbox from", selectedValue, "to", newValue);
        handleChange(newValue);
      }
    }
    // Let Radix UI handle Space key naturally
  };

  // Handle checkbox change
  const handleCheckedChange = (checked: boolean) => {
    console.log("Checkbox changed to:", checked);
    handleChange(checked);
  };

  return (
    <div className="w-full mb-5 flex items-start">
      <Checkbox
        id={checkboxId}
        onCheckedChange={handleCheckedChange}
        checked={selectedValue}
        name={fieldItem?.name}
        disabled={disabled}
        onKeyDown={handleKeyDown}
        tabIndex={disabled ? -1 : 0}
        role="button"
        aria-describedby={errorMessage ? `${checkboxId}-error` : undefined}
        aria-label={label || fieldItem?.name || "Checkbox"}
        className={`rounded-[2px] bg-background border ${
          errorMessage ? "border-error" : "border-border"
        } text-background data-[state=checked]:text-background data-[state=checked]:bg-primary p-0 mt-1.5`}
      />
      <div className="flex flex-col ml-2">
        {label && (
          <FieldTitle
            label={label}
            htmlFor={checkboxId}
            isMandatory={!fieldItem?.displayName && isMandatory}
          />
        )}
        {markdownText && (
          <ReactMarkdown
            className="markDown mt-1"
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[rehypeRaw]}
            components={{ a: LinkRenderer }}
          >
            {markdownText}
          </ReactMarkdown>
        )}
        {errorMessage && (
          <span
            id={`${checkboxId}-error`}
            className="text-sm text-red-500 mt-1"
          >
            {errorMessage}
          </span>
        )}
      </div>
    </div>
  );
}
