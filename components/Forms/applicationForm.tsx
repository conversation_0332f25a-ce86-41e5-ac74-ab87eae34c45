"use client";
import Image from "next/image";
import React, { useEffect, useState, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { sortOrder } from "@/helpers/Sorting";
import {
  getOapDetail,
  getOapForm,
  getOapFormSections,
  saveStudentInfo,
} from "@/api/api";
import loader2 from "../../public/loader2.svg";
import loader from "../../public/loader.svg";
import { useAtom } from "jotai";
import {
  consumerAPIKey,
  dateReplacement,
  preferredDateFormat,
  preferredLanguage,
  programmeName,
  recaptchaEnabledAtom,
  staticContentsAtom,
} from "@/lib/atom";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { FormLayout } from "../custom/formLayout";
import ReactMarkdown from "react-markdown";
import { signUp } from "aws-amplify/auth";
import { getFieldNamesByFormQuery, validatePassword } from "@/lib/utils";
import { useRecaptcha } from "@/hooks/useRecaptcha";
import { verifyRecaptcha } from "@/api/api";
import { toast } from "react-hot-toast";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { LinkRenderer } from "../custom/linkRender";
import { fontSizeAtom } from "@/lib/atom";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";

export default function ApplicationForm() {
  const [pageDetails, setPageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const router = useRouter();
  const [saving, setSaving] = useState<boolean>(false);
  const [apiKey, setApiKey] = useAtom(consumerAPIKey);
  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const [showVerficationMessage, setShowVerficationMessage] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [_, setDateReplacement] = useAtom(dateReplacement);
  const [preferLang] = useAtom(preferredLanguage);
  const [staticContent, setStaticContents] = useAtom(staticContentsAtom);
  const [, setRecaptchaEnabled] = useAtom(recaptchaEnabledAtom);

  const { executeRecaptcha, isRecaptchaLoaded, recaptchaError } =
    useRecaptcha();

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
    reset,
  } = useFormContext();

  const searchParams = useSearchParams();
  const utmSource = searchParams?.get("utm_source");
  const utmMedium = searchParams?.get("utm_medium");
  const utmCampaign = searchParams?.get("utm_campaign");
  const utmContent = searchParams?.get("utm_content");
  const utmTerm = searchParams?.get("utm_term");
  const utmNetwork = searchParams?.get("utm_network");
  const utmReferrer = searchParams?.get("utm_referrer");
  const [fontSize, setFontSize] = useAtom(fontSizeAtom);

  useEffect(() => {
    if (typeof window !== "undefined") {
      const utmParams: Record<string, string> = {};

      if (utmSource) utmParams.utmSource = utmSource;
      if (utmMedium) utmParams.utmMedium = utmMedium;
      if (utmCampaign) utmParams.utmCampaign = utmCampaign;
      if (utmContent) utmParams.utmContent = utmContent;
      if (utmTerm) utmParams.utmTerm = utmTerm;
      if (utmNetwork) utmParams.utmNetwork = utmNetwork;
      if (utmReferrer) utmParams.utmReferrer = utmReferrer;

      // Only store if there are any UTM parameters
      if (Object.keys(utmParams).length > 0) {
        console.log("setting utm params", utmParams);
        sessionStorage.setItem("utmParams", JSON.stringify(utmParams));
      }
    }
  }, []);

  useEffect(() => {
    const onFocus = () => {
      const savedPrefLang = localStorage.getItem("preferredLanguage");
      localStorage.clear();
      if (savedPrefLang) {
        localStorage.setItem("preferredLanguage", savedPrefLang);
      }
    };
    onFocus();
  }, []);

  const {
    data: pageQuery,
    isFetching: pageQueryFetching,
    refetch: refetchPageQuery,
  } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      setStaticContents(res?.staticContents);
      setPreferredDateFormat(res?.preferedDateFormat);
      setDateReplacement(res?.replaceWith);
      setApiKey(res?.eipConsumerKey);
      setRecaptchaEnabled(false);
      if (res?.fontSize) {
        setFontSize(res.fontSize);
      }
      return res;
    },
    enabled: true,
  });

  const enableAutoDetection = useMemo(
    () => pageQuery?.enableAutoDetection,
    [pageQuery?.enableAutoDetection]
  );

  const {
    data: sectionQuery,
    isFetching: sectionQueryFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [`${pageQuery?.PK}-${pageQuery?.SK}-${pageQuery?.landingForm}`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageQuery?.PK,
          form: pageQuery?.registrationForm,
          mode: pageQuery?.SK,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!pageQuery?.PK,
  });
  const {
    data: formQuery,
    isFetching: formQueryFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${sectionQuery?.SK}-${sectionQuery?.section?.[0]?.section}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.section && sectionQuery?.SK)) return;
      let res = await getOapFormSections(
        {
          oap: pageQuery?.PK,
          mode: pageQuery?.SK,
          formName: sectionQuery?.SK,
          sectionName: sectionQuery?.section?.[0]?.section,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!sectionQuery?.SK,
  });
  useEffect(() => {
    reset({});
  }, []);

  useEffect(() => {
    refetchPageQuery();
    if (pageDetails.mode && pageDetails.screen && pageQuery) {
      refetchSectionQuery();
      refetchFormQuery();
    }
  }, [preferLang]);

  const handleRegister = async () => {
    const registrationDetails = getValues();

    // Clean whitespace-only values and update form
    Object.keys(registrationDetails).forEach((key) => {
      const value = registrationDetails[key];
      if (typeof value === "string" && value.trim() === "") {
        setValue(key, "");
        registrationDetails[key] = "";
      }
    });

    const fieldNames = getFieldNamesByFormQuery(formQuery);

    const isValid = await trigger(fieldNames);

    if (!isValid) return;

    registrationDetails.registerEmail =
      registrationDetails?.registerEmail?.toLowerCase();

    setErrorMessage("");
    if (
      registrationDetails?.registerPassword !==
      registrationDetails?.repeatPassword
    ) {
      return setError("repeatPassword", {
        message:
          staticContent?.errors?.userValidation?.passwordMismatch ||
          "Confirm password should be same",
      });
    }

    if (!validatePassword(registrationDetails?.registerPassword)) {
      return setErrorMessage(
        staticContent?.errors?.userValidation?.passwordRequirements ||
          "Password must contain at least one special character, a minimum length of 8 characters, one uppercase and lowercase letter."
      );
    }

    const updatedRest = {
      "custom:firstName": registrationDetails?.firstName,
      "custom:lastName": registrationDetails?.lastName,
      "custom:phoneNumber": registrationDetails?.phoneNumber.numberWithCode,
      "custom:country": registrationDetails?.country?.label,
      email: registrationDetails?.registerEmail,
    };

    if (!isValid) return;
    setSaving((prev) => !prev);

    // Check for reCAPTCHA errors first
    if (pageQuery.recaptchaEnabled && recaptchaError) {
      setSaving(false);
      setErrorMessage(recaptchaError);
      setTimeout(() => setErrorMessage(""), 5000);
      return;
    }

    try {
      if (pageQuery.recaptchaEnabled) {
        const recaptchaToken = await executeRecaptcha("signup");
        if (!recaptchaToken) {
          setSaving(false);
          setErrorMessage("reCAPTCHA verification failed. Please try again.");
          setTimeout(() => setErrorMessage(""), 5000);
          return;
        }

        // Verify reCAPTCHA token
        const recaptchaResult = await verifyRecaptcha(recaptchaToken, "signup");
        if (!recaptchaResult.success) {
          setSaving(false);
          setErrorMessage(
            recaptchaResult.error ||
              "reCAPTCHA verification failed. Please try again."
          );
          setTimeout(() => setErrorMessage(""), 5000);
          return;
        }
      }

      const initialPayload = {
        email: registrationDetails?.registerEmail,
        localization: preferLang,
      };

      await saveStudentInfo(initialPayload, apiKey, {
        oapName: pageDetails?.screen,
      });

      // Proceed with signup if reCAPTCHA is successful
      const response = await signUp({
        username: registrationDetails?.registerEmail,
        password: registrationDetails?.registerPassword,
        options: { userAttributes: updatedRest },
      });

      if (response?.userId) {
        const { registerEmail, ...filteredRegistrationDetails } =
          registrationDetails;

        filteredRegistrationDetails.email = registerEmail;
        filteredRegistrationDetails.isConfirmed = false;
        filteredRegistrationDetails.verificationTriggeredAt = new Date();
        filteredRegistrationDetails.userId = response?.userId;
        filteredRegistrationDetails.localization = preferLang;

        // Add UTM parameters to registration data only if they exist
        const utmParams = sessionStorage.getItem("utmParams");
        if (utmParams) {
          const parsedUtmParams = utmParams ? JSON.parse(utmParams) : {};
          if (Object.keys(parsedUtmParams).length > 0) {
            filteredRegistrationDetails.utmParams = parsedUtmParams;
          }
        }

        delete filteredRegistrationDetails.registerPassword;
        delete filteredRegistrationDetails.repeatPassword;

        await saveStudentInfo(filteredRegistrationDetails, apiKey, {
          oapName: pageDetails?.screen,
        });
        setShowVerficationMessage(true);
      }
    } catch (error: any) {
      setSaving((prev: any) => !prev);
      console.log("error", error.name);

      setErrorMessage(
        staticContent?.errors?.userValidation?.[error?.name] || error?.message
      );
      setTimeout(() => {
        setErrorMessage("");
      }, 5000);

      return error;
    }
  };

  if (showVerficationMessage) {
    const details = getValues();
    return (
      <FormLayout pageQuery={pageQuery}>
        <div
          className=" w-full max-w-lg p-4 bg-background border border-gray-200 shadow sm:p-6  md:px-16 md:py-8"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-4">
            <h3 className="font-bold text-primary dark:text-white pb-6 text-center text-2xl">
              {formQuery?.verificationMessage?.displayName}
            </h3>
          </div>
          <div className="px-5">
            <ReactMarkdown className="markDown text-center ">
              {formQuery?.verificationMessage?.message?.replace(
                "<student-email>",
                details?.registerEmail || "the given email"
              )}
            </ReactMarkdown>
          </div>
        </div>
      </FormLayout>
    );
  }

  if (pageQueryFetching || sectionQueryFetching || formQueryFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      {sectionQuery && sectionQuery?.description ? (
        <section
          className="flex flex-col max-md:w-full xl:w-[83%] justify-center sm:items-start gap-y-10 max-sm:items-start lg:flex-row lg:gap-x-20 px-12 max-md:px-0 rounded-[24px]"
          aria-label="Application form content"
        >
          <div className="items-start pr-8">
            <ReactMarkdown
              className="markDown"
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {sectionQuery?.description}
            </ReactMarkdown>
          </div>

          <div
            className=" w-full md:max-w-[480px] pb-3 max-md:px-0 px-8 bg-background border border-gray-200 shadow mt-4"
            style={{ borderRadius: 8 }}
          >
            <div className="space-y-3 px-12 max-md:px-8 py-8">
              <h2
                className="font-bold dark:text-white pb-4 text-center text-3xl"
                style={getBrandSpecificFontStyle(fontSize, "page-title")}
              >
                {sectionQuery?.section?.[0]?.displayName}
              </h2>

              {sortOrder(formQuery?.fieldData, "indexOrder")?.map(
                (item: any, index: any) => {
                  return (
                    <div key={index}>
                      <DynamicFields
                        register={register}
                        selectedValue={
                          watch(item?.fieldName) ||
                          watch(`${item?.documentType}`) ||
                          ""
                        }
                        displayNoTitle={false}
                        disabled={
                          item?.disabledWhen
                            ? watch(item?.disabledWhen?.fieldName)?.label ===
                              item?.disabledWhen?.value
                            : false
                        }
                        isVisibleWhen
                        fieldItem={item}
                        label={
                          item?.label || item?.displayName || item?.placeholder
                        }
                        handleValueChanged={(value: any, type?: string) => {
                          if (item?.childField && item?.setValue) {
                            if (value?.value == item?.value) {
                              setValue(item?.childField, item?.setValue);
                              clearErrors(item?.childField);
                            } else {
                              setValue(item?.childField, "");
                            }
                          }
                          clearErrors(item?.fieldName);
                          clearErrors(`${item?.documentType}`);
                          if (type === "pickList" && item?.fieldDisplayName) {
                            setValue(item?.fieldDisplayName, value);
                          }
                          if (item?.resetChild) {
                            setValue(item?.resetChild, "");
                            clearErrors(item?.resetChild);
                          }
                          setValue(item?.fieldName, value);
                        }}
                        errorMessage={
                          errors?.[item?.fieldName]?.message ||
                          errors?.[`${item?.documentType}`]?.message
                        }
                        name={item?.fieldName}
                        trigger={trigger}
                        watch={watch}
                        clearErrors={clearErrors}
                        setError={setError}
                        setValue={setValue}
                        enableAutoDetection={enableAutoDetection}
                      />
                    </div>
                  );
                }
              )}

              {errorMessage && (
                <p className="text-error relative ">{errorMessage}</p>
              )}

              <div className="form-action w-full flex justify-center ">
                {formQuery?.fieldData
                  ?.filter((item: any) => item.type == "button")
                  .map((ele: any, i: number) => {
                    return (
                      <button
                        key={i}
                        style={getBrandSpecificFontStyle(fontSize, "label")}
                        onClick={() => {
                          handleRegister();
                        }}
                        disabled={
                          !watch("privacyPolicyConsent") &&
                          pageQuery?.needPrivacyPolicyConsent
                        }
                        className={`text-background rounded bg-primary w-full font-bold text-sm px-5 py-2.5 mt-3 mb-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors ${
                          !watch("privacyPolicyConsent") &&
                          pageQuery?.needPrivacyPolicyConsent
                            ? "opacity-50 cursor-not-allowed"
                            : "hover:bg-secondary"
                        }`}
                      >
                        {saving ? (
                          <div className=" w-full flex items-center justify-center">
                            <Image
                              priority
                              src={loader2}
                              height={20}
                              width={20}
                              alt="Follow us on Twitter"
                            />
                          </div>
                        ) : (
                          ele?.placeholder
                        )}
                      </button>
                    );
                  })}
              </div>
              <div>
                {formQuery?.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 7 && item?.type === "divider"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div
                        key={index}
                        className="mt-7 mb-5 tracking-wide h-[1px] w-full bg-shadow bg-slate-300"
                      />
                    );
                  })}
              </div>
              <div>
                {formQuery?.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 8 && item?.type === "underline"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div key={index} className="tracking-wide">
                        <p className="text-text-primary mb-1.5 text-sm text-center">
                          <button
                            className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                            disabled={item?.disabled}
                          >
                            {item?.text || item?.label}
                          </button>
                        </p>
                      </div>
                    );
                  })}
              </div>
              <div>
                {formQuery?.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 9 && item?.type === "underline"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div key={index} className="tracking-wide">
                        <p className="text-text-primary text-sm text-center">
                          <button
                            onClick={() => router.push("login")}
                            className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                          >
                            {item?.text || item?.label}
                          </button>
                        </p>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </section>
      ) : (
        <div
          className="w-full max-w-[480px] p-9 bg-background border border-gray-200 shadow sm:px-14 sm:py-8 md:px-20 md:py-8"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-4">
            <h2 className="font-bold text-primary dark:text-white pb-6 text-center text-2xl">
              {sectionQuery?.section?.[0]?.displayName}
            </h2>

            {sortOrder(formQuery?.fieldData, "indexOrder")?.map(
              (item: any, index: any) => {
                return (
                  <div key={index}>
                    <DynamicFields
                      register={register}
                      selectedValue={
                        watch(item?.fieldName) ||
                        watch(`${item?.documentType}`) ||
                        ""
                      }
                      displayNoTitle={false}
                      disabled={
                        item?.disabledWhen
                          ? watch(item?.disabledWhen?.fieldName)?.label ===
                            item?.disabledWhen?.value
                          : false
                      }
                      isVisibleWhen
                      fieldItem={item}
                      label={
                        item?.label || item?.displayName || item?.placeholder
                      }
                      handleValueChanged={(value: any, type?: string) => {
                        if (item?.childField && item?.setValue) {
                          if (value?.value == item?.value) {
                            setValue(item?.childField, item?.setValue);
                            clearErrors(item?.childField);
                          } else {
                            setValue(item?.childField, "");
                          }
                        }
                        clearErrors(item?.fieldName);
                        clearErrors(`${item?.documentType}`);
                        if (type === "pickList" && item?.fieldDisplayName) {
                          setValue(item?.fieldDisplayName, value);
                        }
                        if (item?.resetChild) {
                          setValue(item?.resetChild, "");
                          clearErrors(item?.resetChild);
                        }
                        setValue(item?.fieldName, value);
                      }}
                      errorMessage={
                        errors?.[item?.fieldName]?.message ||
                        errors?.[`${item?.documentType}`]?.message
                      }
                      name={item?.fieldName}
                      trigger={trigger}
                      watch={watch}
                      clearErrors={clearErrors}
                      setError={setError}
                      setValue={setValue}
                    />
                  </div>
                );
              }
            )}

            {pageQuery?.needPrivacyPolicyConsent && (
              <div className="space-y-2">
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <Input
                      id="privacyPolicyConsent"
                      type="checkbox"
                      {...register("privacyPolicyConsent", {
                        required:
                          "You must accept the terms and conditions to proceed",
                      })}
                      className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary"
                    />
                  </div>
                  <div className="ml-3 text-xs text-gray-600 w-[90%]">
                    <Label
                      htmlFor="privacyPolicyConsent"
                      className="text-text-primary text-xs"
                    >
                      <ReactMarkdown
                        className="markDown inline"
                        components={{
                          a: LinkRenderer,
                          p: ({ node, ...props }) => <span {...props} />,
                        }}
                      >
                        {formQuery?.termsAndConditionData?.termText}
                      </ReactMarkdown>
                      <span className="text-error">*</span>
                    </Label>
                  </div>
                </div>
                {errors.privacyPolicyConsent && (
                  <p className="text-error text-sm mt-1">
                    {errors.privacyPolicyConsent.message as string}
                  </p>
                )}
              </div>
            )}

            {errorMessage && (
              <p className="text-error relative ">{errorMessage}</p>
            )}

            <div className="form-action w-full flex justify-center ">
              {formQuery?.fieldData
                ?.filter((item: any) => item.type == "button")
                .map((ele: any, i: number) => {
                  return (
                    <button
                      key={i}
                      onClick={() => {
                        handleRegister();
                      }}
                      disabled={
                        !watch("privacyPolicyConsent") &&
                        pageQuery?.needPrivacyPolicyConsent
                      }
                      className={`text-background rounded bg-primary w-full font-bold text-sm px-5 py-2.5 mt-3 mb-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors ${
                        !watch("privacyPolicyConsent") &&
                        pageQuery?.needPrivacyPolicyConsent
                          ? "opacity-50 cursor-not-allowed"
                          : "hover:bg-secondary"
                      }`}
                    >
                      {saving ? (
                        <div className="w-full flex items-center justify-center">
                          <Image
                            priority
                            src={loader2}
                            height={20}
                            width={20}
                            alt="Follow us on Twitter"
                          />
                        </div>
                      ) : (
                        ele?.placeholder
                      )}
                    </button>
                  );
                })}
            </div>
            <div>
              {formQuery?.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 7 && item?.type === "divider"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className="mt-7 mb-5 tracking-wide h-[1px] w-full bg-shadow bg-slate-300"
                    />
                  );
                })}
            </div>
            <div>
              {formQuery?.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 8 && item?.type === "underline"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="tracking-wide">
                      <p className="text-text-primary mb-1.5 text-sm text-center">
                        <button
                          className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                          disabled={item?.disabled}
                        >
                          {item?.text || item?.label}
                        </button>
                      </p>
                    </div>
                  );
                })}
            </div>
            <div>
              {formQuery?.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 9 && item?.type === "underline"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="tracking-wide">
                      <p className="text-text-primary text-sm text-center">
                        <button
                          onClick={() => router.push("login")}
                          className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                        >
                          {item?.text || item?.label}
                        </button>
                      </p>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}
    </FormLayout>
  );
}
