"use client";
import Image from "next/image";
import React, { useEffect, useState, useMemo } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { sortOrder } from "@/helpers/Sorting";
import { confirmSignUp, signIn, signInWithRedirect } from "aws-amplify/auth";

import {
  getLookUpData,
  getOapDetail,
  getOapForm,
  getOapFormSections,
  getStudentInfo,
  saveStudentInfo,
  verifyRecaptcha,
} from "@/api/api";
import loader2 from "../../public/loader2.svg";
import loader from "../../public/loader.svg";
import { useAtom } from "jotai";
import ReactMarkdown from "react-markdown";
import {
  consumerAPIKey,
  dateReplacement,
  email,
  nextForm,
  preferredDateFormat,
  preferredLanguage,
  qualifyingQuestions,
  recaptcha<PERSON><PERSON><PERSON><PERSON><PERSON>,
  static<PERSON>onte<PERSON><PERSON><PERSON>,
  fontSize<PERSON><PERSON>,
} from "@/lib/atom";
import { useFormContext } from "react-hook-form";
import DynamicFields from "../custom/DynamicFields";
import { FormLayout } from "../custom/formLayout";
import { getFieldNamesByFormQuery } from "@/lib/utils";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import { LinkRenderer } from "../custom/linkRender";
import UcwLoginScreen from "../custom-login-screen/ucw-login-screen/ucw-login-screen";
import { useRecaptcha } from "@/hooks/useRecaptcha";
import { toast } from "react-hot-toast";
import { getBrandSpecificFontStyle } from "@/lib/brandUtils";
import { Button } from "../ui/button";
import { LinkedInIcon } from "../icons/LinkedInIcon";
import UegLoginScreen from "../custom-login-screen/ueg-login-screen/ueg-login-screen";

export default function LoginForm() {
  const [_, setQualifyingQuestionsState] = useAtom(qualifyingQuestions);
  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });
  const [staticContent] = useAtom<any>(staticContentsAtom);
  const [isRecaptchaEnabled, setRecaptchaEnabled] =
    useAtom(recaptchaEnabledAtom);

  const router = useRouter();
  const [, setNextFormDetails] = useAtom(nextForm);
  const [saving, setSaving] = useState<boolean>(false);
  const [linkedInLoading, setLinkedInLoading] = useState<boolean>(false);
  const [apiKey, setApiKey] = useAtom(consumerAPIKey);
  const [, setEmail] = useAtom(email);
  const [errorMessage, setErrorMessage] = useState("");
  const [, setDateReplacement] = useAtom(dateReplacement);

  const [, setPreferredDateFormat] = useAtom(preferredDateFormat);
  const { executeRecaptcha, isRecaptchaLoaded, recaptchaError } =
    useRecaptcha();

  const searchParams = useSearchParams();

  const username: any = searchParams?.get("user_name");
  const confirmationCode: any = searchParams?.get("confirmation_code");
  const userEmail: any = searchParams?.get("email");
  const [preferLang, setPreferLang] = useAtom(preferredLanguage);

  const [fontSize, setFontSize] = useAtom(fontSizeAtom);

  useEffect(() => {
    const fetchUserDetails = async () => {
      if (confirmationCode && userEmail) {
        try {
          const userDetails = await getStudentInfo(
            {
              email: userEmail,
              oapName: process.env.NEXT_PUBLIC_OAP_NAME,
            },
            apiKey
          );

          if (
            userDetails?.utmParams &&
            Object.keys(userDetails?.utmParams).length > 0
          ) {
            sessionStorage.setItem(
              "utmParams",
              JSON.stringify(userDetails?.utmParams)
            );
          }
        } catch (error) {
          console.error("Failed to fetch user details:", error);
        }
      }
    };

    fetchUserDetails();
  }, [confirmationCode]);

  useQuery({
    queryKey: ["verifyUser", confirmationCode, username],
    queryFn: async () => {
      try {
        const response = await confirmSignUp({ confirmationCode, username });
        if (response?.isSignUpComplete) {
          await saveStudentInfo(
            { email: userEmail, isConfirmed: true },
            apiKey,
            {
              oapName: pageDetails?.screen,
            }
          );
        }
      } catch (error: any) {
        if (
          error.message ===
          "User cannot be confirmed. Current status is CONFIRMED"
        ) {
          setErrorMessage(
            staticContent?.errors?.userValidation?.alreadyVerified ||
              "This user is already verified. Please login to continue"
          );
        } else {
          setErrorMessage(
            staticContent?.errors?.userValidation?.verificationFailed ||
              "User verification failed"
          );
          console.log("err", error);
        }

        setTimeout(() => {
          setErrorMessage("");
        }, 5000);
      }
    },
    enabled: !!confirmationCode && !!username,
  });

  const {
    register,
    setValue,
    watch,
    formState: { errors },
    clearErrors,
    trigger,
    setError,
    getValues,
    reset,
  } = useFormContext();

  const {
    data: pageQuery,
    isFetching: pageQueryFetching,
    refetch: refetchPageQuery,
  } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      setPreferredDateFormat(res?.preferedDateFormat);
      setDateReplacement(res?.replaceWith);
      setApiKey(res?.eipConsumerKey);
      setRecaptchaEnabled(res?.recaptchaEnabled);
      if (res?.fontSize) {
        setFontSize(res.fontSize);
      }
      return res;
    },
    enabled: true,
  });

  const enableAutoDetection = useMemo(
    () => pageQuery?.enableAutoDetection,
    [pageQuery?.enableAutoDetection]
  );

  const {
    data: sectionQuery,
    isFetching: sectionQueryFetching,
    refetch: refetchSectionQuery,
  } = useQuery({
    queryKey: [`${pageQuery?.PK}-${pageQuery?.SK}-${pageQuery?.landingForm}`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageQuery?.PK,
          form: pageQuery?.landingForm,
          mode: pageQuery?.SK,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );
      return res;
    },
    enabled: !!pageQuery?.PK,
  });

  const { data: countries } = useQuery({
    queryKey: [`oap/lookup/country`],
    queryFn: async () => {
      let res = await getLookUpData(
        {
          name: "oap/lookup/country",
        },
        apiKey
      );

      return res;
    },
    enabled: !!apiKey,
  });

  const {
    data: formQuery,
    isFetching: formQueryFetching,
    refetch: refetchFormQuery,
  } = useQuery({
    queryKey: [
      `${pageQuery?.PK}-${pageQuery?.SK}-${sectionQuery?.SK}-${sectionQuery?.section?.[0]?.section}`,
    ],
    queryFn: async () => {
      if (!(sectionQuery?.section?.[0]?.section && sectionQuery?.SK)) return;
      let res = await getOapFormSections(
        {
          oap: pageQuery?.PK,
          mode: pageQuery?.SK,
          formName: sectionQuery?.SK,
          sectionName: sectionQuery?.section?.[0]?.section,
          ...(preferLang === "de" && { language: "de" }),
        },
        apiKey
      );

      return res;
    },
    enabled: !!sectionQuery?.SK,
  });

  useQuery({
    queryKey: [`next-form-screen`],
    queryFn: async () => {
      let tempObj = {};
      formQuery?.fieldData?.forEach((ele: any) => {
        if (ele.action == "nextForm") {
          tempObj = { ...tempObj, ...ele[ele.action] };
        }
      });
      return tempObj;
    },
    enabled: formQuery?.fieldData.length > 0,
  });

  const getNextForm = () => {
    const formDetail = formQuery?.fieldData?.find(
      (item: any) => item?.type === "button" && item?.indexOrder === 3
    )?.nextForm;
    return formDetail;
  };

  useEffect(() => {
    reset({});
  }, [reset]);

  useEffect(() => {
    refetchPageQuery();
    if (pageQuery && apiKey) {
      refetchSectionQuery();
      refetchFormQuery();
    }
  }, [preferLang]);

  const handleLinkedInLogin = async () => {
    setLinkedInLoading(true);
    setErrorMessage("");

    try {
      // Use signInWithRedirect for LinkedIn OAuth
      await signInWithRedirect({ provider: { custom: "linkedin" } });
    } catch (error: any) {
      setLinkedInLoading(false);
      console.error("LinkedIn authentication error:", error);
      setErrorMessage(
        staticContent?.errors?.userValidation?.linkedInAuthFailed ||
          "LinkedIn authentication failed. Please try again."
      );
      setTimeout(() => {
        setErrorMessage("");
      }, 5000);
    }
  };

  const handleLogin = async () => {
    const loginDetails = getValues();

    Object.keys(loginDetails).forEach((key) => {
      const value = loginDetails[key];
      if (typeof value === "string" && value.trim() === "") {
        setValue(key, "");
        loginDetails[key] = "";
      }
    });

    const fieldNames = getFieldNamesByFormQuery(formQuery);

    const isValid = await trigger(fieldNames);
    if (!isValid) return;

    loginDetails.loginEmail = loginDetails?.loginEmail?.toLowerCase();

    setSaving((prev) => !prev);
    setErrorMessage("");

    // Check for reCAPTCHA errors first
    if (pageQuery.recaptchaEnabled && recaptchaError) {
      setSaving(false);
      setErrorMessage(recaptchaError);
      setTimeout(() => setErrorMessage(""), 5000);
      return;
    }

    // Execute reCAPTCHA verification
    try {
      if (pageQuery.recaptchaEnabled) {
        const recaptchaToken = await executeRecaptcha("login");
        if (!recaptchaToken) {
          setSaving(false);
          setErrorMessage("reCAPTCHA verification failed. Please try again.");
          setTimeout(() => setErrorMessage(""), 5000);
          return;
        }

        // Verify reCAPTCHA token
        const recaptchaResult = await verifyRecaptcha(recaptchaToken, "login");
        console.log("recaptchaResult", recaptchaResult);
        if (!recaptchaResult.success) {
          setSaving(false);
          setErrorMessage(
            recaptchaResult.error ||
              "reCAPTCHA verification failed. Please try again."
          );
          setTimeout(() => setErrorMessage(""), 5000);
          return;
        }
      }

      // Proceed with login if reCAPTCHA is successful
      const response: any = await signIn({
        username: loginDetails?.loginEmail,
        password: loginDetails?.loginPassword,
      });

      if (response?.isSignedIn) {
        const userDetails: any = await getStudentInfo(
          {
            email: loginDetails?.loginEmail,
            oapName: process.env.NEXT_PUBLIC_OAP_NAME,
          },
          apiKey
        );
        const updatedUserDetails: any = {};

        const getCountryCodeByLabel = (label: any) => {
          const country = countries?.find(
            (c: any) => c?.label?.toLowerCase() === label?.toLowerCase()
          );
          return country ? country?.value : label;
        };

        for (const key in userDetails) {
          if (typeof userDetails[key] === "object" && key !== "phoneNumber") {
            updatedUserDetails[key] = getCountryCodeByLabel(
              userDetails[key].value
            );
            updatedUserDetails[`${key}DisplayName`] = userDetails[key].label;
          } else {
            updatedUserDetails[key] = userDetails[key];
          }
        }

        const keysToDelete = [
          "PK",
          "SK",
          "createdAt",
          "isConfirmed",
          "updatedAt",
          "verificationTriggeredAt",
        ];
        keysToDelete.forEach((key) => {
          delete updatedUserDetails[key];
        });
        setEmail(loginDetails?.loginEmail);

        localStorage.setItem(
          "basic-details",
          JSON.stringify(updatedUserDetails)
        );

        if (
          !userDetails?.privacyPolicyConsent &&
          pageQuery?.needPrivacyPolicyConsent
        ) {
          router.push("/privacy-policy");
        } else {
          if (formQuery?.havePreQualificationQuestions) {
            if (!userDetails?.isPreQualificationQuestionsSubmitted) {
              router.push("/app-prequalifying-questions");
            } else {
              setQualifyingQuestionsState({
                completedPostStudies: userDetails?.completedPostStudies,
                hasMeetGPA: userDetails?.hasMeetGPA,
                hasMeetProficiencyScore: userDetails?.hasMeetProficiencyScore,
              });
              router.push("/application-filter");
            }
          } else {
            router.push("/application-filter");
          }
        }

        await saveStudentInfo(
          { email: loginDetails?.loginEmail, localization: preferLang },
          apiKey,
          {
            oapName: pageDetails?.screen,
          }
        );

        setNextFormDetails(getNextForm());
      } else {
        setSaving((prev) => !prev);
        setErrorMessage(
          staticContent?.errors?.userValidation?.invalidUser || "Invalid user"
        );
        setTimeout(() => {
          setErrorMessage("");
        }, 5000);
      }
    } catch (error: any) {
      if (error?.message?.includes("User must reset password before login")) {
        localStorage.setItem("forceResetPassword", "true");
        router.push("/forgot-password");
        return;
      }
      setSaving((prev) => !prev);
      console.log("error", error.name);
      if (error?.message) {
        setErrorMessage(
          staticContent?.errors?.userValidation?.[error?.name] || error?.message
        );
        setTimeout(() => {
          setErrorMessage("");
        }, 5000);
      }
      return error;
    }

    setTimeout(() => {
      setErrorMessage("");
    }, 5000);
  };

  if (pageQueryFetching || sectionQueryFetching || formQueryFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <Image
          priority
          src={loader}
          height={32}
          width={32}
          alt="Follow us on Twitter"
        />
      </main>
    );
  }

  if (process.env.NEXT_PUBLIC_OAP_NAME === "UCW") {
    return (
      <UcwLoginScreen
        handleLogin={handleLogin}
        formQuery={formQuery}
        errorMessage={errorMessage}
        setErrorMessage={setErrorMessage}
        isRecaptchaEnabled={pageQuery?.recaptchaEnabled}
      />
    );
  } else if (process.env.NEXT_PUBLIC_OAP_NAME === "UEG") {
    return (
      <UegLoginScreen
        handleLogin={handleLogin}
        formQuery={formQuery}
        errorMessage={errorMessage}
        setErrorMessage={setErrorMessage}
        isRecaptchaEnabled={pageQuery?.recaptchaEnabled}
        pageQuery={pageQuery}
      />
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      {sectionQuery && sectionQuery?.description ? (
        <section
          className="flex flex-col max-md:w-full xl:w-[83%] justify-center sm:items-center gap-y-10 max-sm:items-start lg:flex-row lg:gap-x-20 px-12 max-md:px-0 rounded-[24px]"
          aria-label="Login form content"
        >
          <div className="flex-1 pr-8">
            <ReactMarkdown
              className="markDown"
              remarkPlugins={[remarkGfm]}
              rehypePlugins={[rehypeRaw]}
            >
              {sectionQuery?.description}
            </ReactMarkdown>
          </div>
          <div
            className=" w-full md:max-w-[480px] pb-3 px-8 max-md:px-0 bg-background border border-gray-200 shadow mt-4"
            style={{ borderRadius: 8 }}
          >
            <div className="space-y-3 px-12 max-md:px-8 py-8">
              <h1
                className="font-bold text-text-primary pb-4 dark:text-white text-center text-3xl"
                style={getBrandSpecificFontStyle(fontSize, "page-title")}
              >
                {sectionQuery?.section?.[0]?.displayName}
              </h1>

              {sortOrder(formQuery?.fieldData, "indexOrder")
                ?.filter((item: any) => item?.indexOrder <= 2)
                ?.map((item: any, index: any) => {
                  return (
                    <div key={index}>
                      <DynamicFields
                        register={register}
                        selectedValue={
                          watch(item?.fieldName) ||
                          watch(`${item?.documentType}`) ||
                          ""
                        }
                        disabled={
                          item?.disabledWhen
                            ? watch(item?.disabledWhen?.fieldName)?.label ===
                              item?.disabledWhen?.value
                            : false
                        }
                        isVisibleWhen
                        fieldItem={item}
                        label={
                          item?.label || item?.displayName || item?.placeholder
                        }
                        handleValueChanged={(value: any, type?: string) => {
                          if (item?.childField && item?.setValue) {
                            if (value?.value == item?.value) {
                              setValue(item?.childField, item?.setValue);
                              clearErrors(item?.childField);
                            } else {
                              setValue(item?.childField, "");
                            }
                          }
                          clearErrors(item?.fieldName);
                          clearErrors(`${item?.documentType}`);
                          if (type === "pickList" && item?.fieldDisplayName) {
                            setValue(item?.fieldDisplayName, value);
                          }
                          if (item?.resetChild) {
                            setValue(item?.resetChild, "");
                            clearErrors(item?.resetChild);
                          }
                          setValue(item?.fieldName, value);
                        }}
                        errorMessage={
                          errors?.[item?.fieldName]?.message ||
                          errors?.[`${item?.documentType}`]?.message
                        }
                        name={item?.fieldName}
                        trigger={trigger}
                        watch={watch}
                        clearErrors={clearErrors}
                        setError={setError}
                        displayNoTitle={false}
                        setValue={setValue}
                        enableAutoDetection={enableAutoDetection}
                      />
                    </div>
                  );
                })}

              {errorMessage && (
                <p className="text-error relative ">{errorMessage}</p>
              )}

              <div className="form-action w-full flex justify-center ">
                {formQuery?.fieldData
                  ?.filter(
                    (item: any) =>
                      item.type == "button" && item?.indexOrder === 3
                  )
                  .map((ele: any, i: number) => {
                    return (
                      <button
                        key={i}
                        onClick={() => {
                          handleLogin();
                        }}
                        className="text-background rounded bg-primary w-full hover:bg-secondary font-bold text-sm px-5 py-2.5 mt-3 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors"
                        style={getBrandSpecificFontStyle(fontSize, "label")}
                      >
                        {saving ? (
                          <div className=" w-full flex items-center justify-center">
                            <Image
                              priority
                              src={loader2}
                              height={20}
                              width={20}
                              alt="Follow us on Twitter"
                            />
                          </div>
                        ) : (
                          ele?.placeholder
                        )}
                      </button>
                    );
                  })}
              </div>

              {formQuery?.fieldData
                ?.filter(
                  (item: any) => item.type == "text" && item?.indexOrder === 4
                )
                .map((ele: any, i: number) => (
                  <p key={i} className="text-center">
                    {ele?.label}
                  </p>
                ))}

              <div className="form-action w-full flex justify-center">
                {formQuery?.fieldData
                  ?.filter(
                    (item: any) =>
                      item.type == "button" && item?.indexOrder === 5
                  )
                  .map((ele: any, i: number) => {
                    return (
                      <button
                        key={i}
                        onClick={() => {
                          router.push("/application");
                        }}
                        className="text-[#696a6a] rounded bg-neutral-200 w-full hover:bg-neutral-300 font-bold text-sm px-5 py-2.5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors"
                        style={getBrandSpecificFontStyle(fontSize, "label")}
                      >
                        {ele?.placeholder}
                      </button>
                    );
                  })}
              </div>
              <div>
                {formQuery.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 6 && item?.type === "divider"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div
                        key={index}
                        className="mt-7 mb-5 tracking-wide h-[1px] w-full bg-shadow bg-slate-300"
                      />
                    );
                  })}
              </div>
              <div>
                {formQuery.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 7 && item?.type === "underline"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div key={index} className="tracking-wide">
                        <p className="text-text-primary mb-1.5 text-sm text-center">
                          <button
                            className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                            disabled={item?.disabled}
                          >
                            {item?.text || item?.label}
                          </button>
                        </p>
                      </div>
                    );
                  })}
              </div>
              <div>
                {formQuery.fieldData
                  ?.filter(
                    (item: any) =>
                      item.indexOrder === 8 && item?.type === "underline"
                  )
                  ?.map((item: any, index: number) => {
                    return (
                      <div key={index} className="tracking-wide">
                        <p className="text-text-primary text-sm text-center">
                          <button
                            onClick={() => {
                              router.push(`${item?.link}`);
                            }}
                            className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                          >
                            {item?.text || item?.label}
                          </button>
                        </p>
                      </div>
                    );
                  })}
              </div>
            </div>
          </div>
        </section>
      ) : (
        <div
          className="w-full md:max-w-[450px] p-9 bg-background border border-gray-200 shadow max-md:px-0"
          style={{ borderRadius: 8 }}
        >
          <div className="space-y-3 px-0">
            <h2 className="font-bold text-text-primary pb-6 dark:text-white text-center text-2xl">
              {sectionQuery?.section?.[0]?.displayName}
            </h2>

            {sortOrder(formQuery?.fieldData, "indexOrder")
              ?.filter((item: any) => item?.indexOrder <= 2)
              ?.map((item: any, index: any) => {
                return (
                  <div key={index}>
                    <DynamicFields
                      register={register}
                      selectedValue={
                        watch(item?.fieldName) ||
                        watch(`${item?.documentType}`) ||
                        ""
                      }
                      disabled={
                        item?.disabledWhen
                          ? watch(item?.disabledWhen?.fieldName)?.label ===
                            item?.disabledWhen?.value
                          : false
                      }
                      isVisibleWhen
                      fieldItem={item}
                      label={
                        item?.label || item?.displayName || item?.placeholder
                      }
                      handleValueChanged={(value: any, type?: string) => {
                        if (item?.childField && item?.setValue) {
                          if (value?.value == item?.value) {
                            setValue(item?.childField, item?.setValue);
                            clearErrors(item?.childField);
                          } else {
                            setValue(item?.childField, "");
                          }
                        }
                        clearErrors(item?.fieldName);
                        clearErrors(`${item?.documentType}`);
                        if (type === "pickList" && item?.fieldDisplayName) {
                          setValue(item?.fieldDisplayName, value);
                        }
                        if (item?.resetChild) {
                          setValue(item?.resetChild, "");
                          clearErrors(item?.resetChild);
                        }
                        setValue(item?.fieldName, value);
                      }}
                      errorMessage={
                        errors?.[item?.fieldName]?.message ||
                        errors?.[`${item?.documentType}`]?.message
                      }
                      name={item?.fieldName}
                      trigger={trigger}
                      watch={watch}
                      clearErrors={clearErrors}
                      setError={setError}
                      displayNoTitle={false}
                      setValue={setValue}
                      enableAutoDetection={enableAutoDetection}
                    />
                  </div>
                );
              })}

            <div className="form-action w-full flex justify-center ">
              {formQuery?.fieldData
                ?.filter(
                  (item: any) => item.type == "button" && item?.indexOrder === 3
                )
                .map((ele: any, i: number) => {
                  return (
                    <button
                      key={i}
                      onClick={() => {
                        handleLogin();
                      }}
                      className="text-background rounded bg-primary w-full hover:bg-secondary font-bold text-sm px-5 py-2.5 mt-3 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors"
                      style={getBrandSpecificFontStyle(fontSize, "label")}
                    >
                      {saving ? (
                        <div className=" w-full flex items-center justify-center">
                          <Image
                            priority
                            src={loader2}
                            height={20}
                            width={20}
                            alt="Follow us on Twitter"
                          />
                        </div>
                      ) : (
                        ele?.placeholder
                      )}
                    </button>
                  );
                })}
            </div>

            {pageQuery.isLinkedInSupport ? (
              <>
                {" "}
                <Button
                  onClick={handleLinkedInLogin}
                  disabled={linkedInLoading || saving}
                  className="text-white rounded bg-[#0077B5] w-full hover:bg-[#005885] font-bold text-sm px-5 py-2.5 mt-3 flex items-center justify-center gap-2"
                >
                  {linkedInLoading ? (
                    <div className="w-full flex items-center justify-center">
                      <Image
                        priority
                        src={loader2}
                        height={20}
                        width={20}
                        alt="Loading"
                      />
                    </div>
                  ) : (
                    <>
                      <LinkedInIcon size={20} className="text-white" />
                      Sign in with LinkedIn
                    </>
                  )}
                </Button>
                {errorMessage && (
                  <p className="text-error relative ">{errorMessage}</p>
                )}
              </>
            ) : null}

            {formQuery?.fieldData
              ?.filter(
                (item: any) => item.type == "text" && item?.indexOrder === 4
              )
              .map((ele: any, i: number) => (
                <p key={i} className="text-center">
                  {ele?.label}
                </p>
              ))}

            <div className="form-action w-full flex justify-center">
              {formQuery?.fieldData
                ?.filter(
                  (item: any) => item.type == "button" && item?.indexOrder === 5
                )
                .map((ele: any, i: number) => {
                  return (
                    <button
                      key={i}
                      onClick={() => {
                        router.push("/application");
                      }}
                      className="text-[#696a6a] rounded bg-neutral-200 w-full hover:bg-neutral-300 font-bold text-sm px-5 py-2.5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 transition-colors"
                    >
                      {ele?.placeholder}
                    </button>
                  );
                })}
            </div>
            <div>
              {formQuery.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 6 && item?.type === "divider"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div
                      key={index}
                      className="mt-7 mb-5 tracking-wide h-[1px] w-full bg-shadow bg-slate-300"
                    />
                  );
                })}
            </div>
            <div>
              {formQuery.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 7 && item?.type === "underline"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="tracking-wide">
                      <p className="text-text-primary mb-1.5 text-sm text-center">
                        <button
                          className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                          disabled={item?.disabled}
                        >
                          {item?.text || item?.label}
                        </button>
                      </p>
                    </div>
                  );
                })}
            </div>
            <div>
              {formQuery.fieldData
                ?.filter(
                  (item: any) =>
                    item.indexOrder === 8 && item?.type === "underline"
                )
                ?.map((item: any, index: number) => {
                  return (
                    <div key={index} className="tracking-wide">
                      <p className="text-text-primary text-sm text-center">
                        <button
                          onClick={() => {
                            router.push(`${item?.link}`);
                          }}
                          className="underline cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-sm transition-colors"
                        >
                          {item?.text || item?.label}
                        </button>
                      </p>
                    </div>
                  );
                })}
            </div>
          </div>
        </div>
      )}
    </FormLayout>
  );
}
