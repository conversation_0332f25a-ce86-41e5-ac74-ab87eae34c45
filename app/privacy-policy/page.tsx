"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useAtom } from "jotai";
import { useQuery } from "@tanstack/react-query";
import ReactMarkdown from "react-markdown";

import { qualifyingQuestions, nextForm, consumerAPIKey } from "@/lib/atom";
import { FormLayout } from "@/components/custom/formLayout";
import { getOapDetail, getOapForm, saveStudentInfo } from "@/api/api";
import loader from "../../public/loader.svg";
import loader2 from "../../public/loader2.svg";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { LinkRenderer } from "@/components/custom/linkRender";
import { email } from "@/lib/atom";
import { useFormContext } from "react-hook-form";

export default function PrivacyPolicy() {
  const {
    register,
    formState: { errors },
  } = useFormContext();
  const [isAccepted, setIsAccepted] = useState(false);
  const [saving, setSaving] = useState(false);
  const router = useRouter();
  const [, setQualifyingQuestionsState] = useAtom(qualifyingQuestions);
  const [, setNextFormDetails] = useAtom(nextForm);
  const [apiKey] = useAtom(consumerAPIKey);
  const [userEmail] = useAtom(email);
  const [pageDetails] = useState({
    screen: process.env.NEXT_PUBLIC_OAP_NAME,
    mode: process.env.NEXT_PUBLIC_OAP_MODE,
  });

  const { data: pageQuery, isFetching: pageQueryFetching } = useQuery({
    queryKey: [`${pageDetails.screen}-${pageDetails.mode}`],
    queryFn: async () => {
      let res = await getOapDetail(
        {
          name: pageDetails.screen,
          mode: pageDetails.mode,
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  const { data: formQuery, isFetching: formQueryFetching } = useQuery({
    queryKey: [`${pageDetails?.screen}-${pageDetails?.mode}-form`],
    queryFn: async () => {
      let res = await getOapForm(
        {
          oap: pageDetails?.screen,
          form: "PRIVACY_POLICY",
          mode: pageDetails?.mode,
        },
        apiKey
      );
      return res;
    },
    enabled: true,
  });

  const handleContinue = async () => {
    // Get user details from localStorage
    const userDetails = JSON.parse(
      localStorage.getItem("basic-details") || "{}"
    );
    setSaving(true);

    try {
      let res = await saveStudentInfo(
        {
          email: userEmail,
          privacyPolicyConsent: true,
        },
        apiKey,
        {
          oapName: pageDetails?.screen,
        }
      );
    } catch (error) {
      console.error("Failed to save privacy policy consent:", error);
      setSaving(false);
      return;
    }

    if (userDetails?.isPreQualificationQuestionsSubmitted === false) {
      router.push("/app-prequalifying-questions");
    } else if (userDetails?.isPreQualificationQuestionsSubmitted) {
      setQualifyingQuestionsState({
        completedPostStudies: userDetails?.completedPostStudies,
        hasMeetGPA: userDetails?.hasMeetGPA,
        hasMeetProficiencyScore: userDetails?.hasMeetProficiencyScore,
      });
      router.push("/application-filter");
    } else {
      router.push("/application-filter");
    }
  };

  if (pageQueryFetching) {
    return (
      <main className="min-h-screen bg-on-background flex flex-col items-center justify-center overflow-scroll bg-contain bg-no-repeat bg-center">
        <Image priority src={loader} height={32} width={32} alt="Loading..." />
      </main>
    );
  }

  return (
    <FormLayout pageQuery={pageQuery}>
      <div
        className="w-full max-w-[500px] p-9 bg-background border border-gray-200 shadow sm:px-14 sm:py-8 md:px-20 md:py-8"
        style={{ borderRadius: 8 }}
      >
        <div className="space-y-6">
          <h5 className="font-bold text-text-primary  dark:text-white text-center text-2xl">
            {formQuery?.termsAndConditionData?.displayName}
          </h5>

          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <Input
                type="checkbox"
                id="privacyPolicyConsent"
                {...register("privacyPolicyConsent", {
                  required:
                    "You must accept the terms and conditions to proceed",
                })}
                checked={isAccepted}
                onChange={(e) => setIsAccepted(e.target.checked)}
                className="mt-1 w-5 h-5 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary"
                aria-label="Accept terms and conditions"
              />
              <Label
                htmlFor="privacyPolicyConsent"
                className="text-text-primary text-sm"
              >
                <ReactMarkdown
                  className="markDown"
                  components={{ a: LinkRenderer }}
                >
                  {formQuery?.termsAndConditionData?.termText}
                </ReactMarkdown>
              </Label>
            </div>

            <div className="flex justify-center mt-8">
              <Button
                onClick={handleContinue}
                disabled={!isAccepted || saving}
                className={`${
                  isAccepted
                    ? "text-background rounded bg-primary w-full hover:bg-secondary font-bold text-sm px-5 py-2.5"
                    : "text-[#696a6a] rounded bg-neutral-200 w-full font-bold text-sm px-5 py-2.5 cursor-not-allowed"
                }`}
              >
                {saving ? (
                  <div className="w-full flex items-center justify-center">
                    <Image
                      priority
                      src={loader2}
                      height={20}
                      width={20}
                      alt="Loading..."
                    />
                  </div>
                ) : (
                  "Continue"
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </FormLayout>
  );
}
