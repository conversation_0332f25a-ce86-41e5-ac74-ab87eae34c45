"use client";
import { fetchAuthSession, signIn, signOut } from "aws-amplify/auth";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import {
  CognitoAccessToken,
  CognitoIdToken,
  CognitoRefreshToken,
  CognitoUser,
  CognitoUserPool,
  CognitoUserSession,
} from "amazon-cognito-identity-js";
import { getAwsConfiguration } from "@/helpers/getAwsConfig";
import { useAtom } from "jotai";
import {
  appHeroAtom,
  applicationId,
  consumerAPIKey,
  email,
  nextForm,
} from "@/lib/atom";
import { getAccessToken, getStudentDetails } from "@/api/api";
import DynamicAuthProgress, {
  AuthenticationPhase,
} from "@/components/custom/dynamic-auth-progress";
import { APPHERO_URL } from "@/helpers/appHeroUrl";

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Memoize UTM parameters to prevent unnecessary re-renders
  const utmParams = useMemo(() => {
    if (!searchParams) return {};

    const params: Record<string, string> = {};
    const utmKeys = [
      "utm_source",
      "utm_medium",
      "utm_campaign",
      "utm_content",
      "utm_term",
      "utm_network",
      "utm_referrer",
    ];

    utmKeys.forEach((key) => {
      const value = searchParams.get(key);
      if (value) params[key] = value;
    });

    return params;
  }, [searchParams]);

  const isIframe = searchParams?.get("fromAppHero") === "true";

  const messageReceivedRef = useRef(false);
  const [authPhase, setAuthPhase] =
    useState<AuthenticationPhase>("initializing");
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Atom state
  const [, setIsAppHero] = useAtom(appHeroAtom);
  const [, setUserEmail] = useAtom(email);
  const [, setApplication] = useAtom(applicationId);
  const [, setApiKey] = useAtom(consumerAPIKey);
  const [, setNextFormDetails] = useAtom(nextForm);

  useEffect(() => {
    if (typeof window !== "undefined" && Object.keys(utmParams).length > 0) {
      console.log("setting utm params", utmParams);
      sessionStorage.setItem("utmParams", JSON.stringify(utmParams));
    }
  }, [utmParams]);

  // Function to set up Amplify session with tokens
  const setupAmplifySession = async (
    username: string,
    accessToken: string,
    idToken: string,
    refreshToken: string,
    userPoolId: string,
    appClientId: string
  ) => {
    try {
      // Create Cognito tokens
      const accessCognitoToken = new CognitoAccessToken({
        AccessToken: accessToken,
      });
      const idCognitoToken = new CognitoIdToken({
        IdToken: idToken,
      });
      const refreshCognitoToken = new CognitoRefreshToken({
        RefreshToken: refreshToken,
      });

      // Create a new user session with the tokens
      const session = new CognitoUserSession({
        AccessToken: accessCognitoToken,
        IdToken: idCognitoToken,
        RefreshToken: refreshCognitoToken,
      });

      // Create a user pool object
      const userPool = new CognitoUserPool({
        UserPoolId: userPoolId,
        ClientId: appClientId,
      });

      // Create a new user object from that pool
      const user = new CognitoUser({
        Username: username,
        Pool: userPool,
      });

      // Connect user to the session
      user.setSignInUserSession(session);

      console.log("Successfully set up Amplify session with tokens");
      return true;
    } catch (error) {
      console.error("Error setting up Amplify session:", error);
      return false;
    }
  };

  // Function to decode JWT token and extract scope
  const decodeAccessToken = (accessToken: string): string | null => {
    try {
      const payload = JSON.parse(atob(accessToken.split(".")[1]));
      const scope: string = payload.scope || "";
      const match = scope.match(/x-api-key_(.+?)\//);
      return match ? match[1] : null;
    } catch (error) {
      console.error("Error decoding access token:", error);
      return null;
    }
  };

  // Function to get next form configuration
  const getNextForm = () => {
    return {
      mode: String(process.env.NEXT_PUBLIC_OAP_MODE || ""),
      type: "single",
      form: "APPLICATION",
      oap: String(process.env.NEXT_PUBLIC_OAP_NAME || ""),
    };
  };

  // Memoize atom setters to prevent unnecessary re-renders
  const atomSetters = useMemo(
    () => ({
      setUserEmail,
      setApplication,
      setNextFormDetails,
      setIsAppHero,
      setApiKey,
    }),
    [setUserEmail, setApplication, setNextFormDetails, setIsAppHero, setApiKey]
  );

  // Function to handle bypass login using temp credentials
  const handleBypassLogin = useCallback(
    async (
      iframeEmail: string,
      iframeAppId: string,
      extractedApiKey: string
    ) => {
      try {
        console.log("Attempting bypass login with temp credentials");
        console.log("Using email:", iframeEmail, "and appId:", iframeAppId);

        // Update authentication phase
        setAuthPhase("validating_session");

        try {
          await signOut();
        } catch (error) {
          console.error("Error signing out:", error);
        }

        // Update to processing credentials phase
        setAuthPhase("processing_credentials");

        // Use temp credentials to bypass Cognito
        const response: any = await signIn({
          username: "<EMAIL>",
          password: "5wZhgxNqEjNXz4T@",
        });

        console.log("Sign in response:", response);

        if (response?.isSignedIn) {
          console.log(
            "Bypass login successful, setting up local storage with iframe data"
          );

          // Update to fetching user data phase
          setAuthPhase("fetching_user_data");

          // Use iframe payload email and appId for local storage
          atomSetters.setUserEmail(String(iframeEmail));
          atomSetters.setApplication(String(iframeAppId));
          atomSetters.setNextFormDetails(getNextForm());
          atomSetters.setIsAppHero(true);

          // Store in localStorage as well for compatibility
          localStorage.setItem(
            "basic-details",
            JSON.stringify({ email: iframeEmail })
          );

          console.log(
            "Set user details, now getting student details with apiKey:",
            extractedApiKey
          );

          // Get application details using iframe data
          try {
            // Parallelize access token retrieval with other operations
            const accessTokenPromise = getAccessToken();

            const studentDetails = await getStudentDetails(
              process.env.NEXT_PUBLIC_OAP_NAME || "",
              iframeEmail,
              iframeAppId,
              extractedApiKey,
              await accessTokenPromise
            );

            console.log("studentDetails", studentDetails);

            if (studentDetails) {
              console.log(
                "Successfully retrieved student details",
                studentDetails
              );

              // Update to preparing dashboard phase
              setAuthPhase("preparing_dashboard");

              // Navigate immediately based on application status
              if (studentDetails?.applicationStatus === "submitted") {
                setAuthPhase("redirecting");
                router.push(`/thank-you`);
              } else if (studentDetails?.program) {
                // Set next form details based on student data
                const nextFormConfig = {
                  mode: String(process.env.NEXT_PUBLIC_OAP_MODE || ""),
                  type: studentDetails?.applicationType ? "multiple" : "single",
                  form: "APPLICATION",
                  oap: String(process.env.NEXT_PUBLIC_OAP_NAME || ""),
                  currentOap: String(
                    studentDetails?.applicationType || "APPLICATION"
                  ),
                };

                atomSetters.setNextFormDetails(nextFormConfig);
                setAuthPhase("redirecting");

                // Navigate based on application type
                if (studentDetails?.applicationType) {
                  router.push(
                    `/form?apply=${studentDetails?.applicationType}&step=0`
                  );
                } else {
                  router.push(`/form?apply=APPLICATION&step=0`);
                }
              } else {
                console.log("No program found, redirecting to login");
                setAuthPhase("redirecting");
                router.push("/login");
              }
            } else {
              console.error("Failed to retrieve student details");
              router.push("/login");
            }
          } catch (error) {
            console.error("Error retrieving student details:", error);
            router.push("/login");
          }
        } else {
          console.error("Bypass login failed");
          router.push("/login");
        }
      } catch (error) {
        console.error("Error during bypass login:", error);
        router.push("/login");
      }
    },
    [router, atomSetters]
  );
  useEffect(() => {
    if (!isIframe) return;

    // Function to send iframe ready message to parent window
    const sendIframeReadyMessage = () => {
      try {
        const message = {
          type: "ready_state",
          timestamp: new Date().getTime(),
        };

        // Send message to parent window
        if (window.parent && window.parent !== window) {
          window.parent.postMessage(
            message,
            APPHERO_URL[
              (process.env.NEXT_PUBLIC_NODE_ENV || "dev").toLowerCase()
            ]
          );
          console.log("Sent ready_state message to parent window");
        }
      } catch (error) {
        console.error("Error sending iframe ready message:", error);
      }
    };

    // Send the ready message immediately after component has mounted
    console.log("Sending iframe ready message");
    sendIframeReadyMessage();

    // No cleanup needed since we're not using timers
  }, [isIframe]);

  useEffect(() => {
    if (!isIframe) return;

    // Flag to prevent multiple processing
    let isProcessingCode = false;

    // Handler for message events containing authorization code
    const handleMessageEvent = async (event: any) => {
      console.log("Message event received:", event.data);

      // Security check: Validate the origin of the message
      const trustedOrigins = [
        "https://stage.apphero.io",
        "http://localhost:3001",
        "*",
        "http://localhost:3000",
        "https://www.apphero.io",
      ];

      if (!trustedOrigins.includes(event.origin)) {
        console.error("Message received from untrusted origin:", event.origin);
        return;
      }

      // Check if we're already processing a code or if the event is invalid
      if (isProcessingCode || !event || !event.data) {
        console.log(
          "Skipping event processing: already processing or invalid event"
        );
        return;
      }

      if (event?.data?.type === "AUTH_REQUEST") {
        console.log("AUTH_REQUEST detected, calling handleMessageEvent");

        // Start authentication process
        setIsAuthenticating(true);
        setAuthPhase("validating_session");

        // Extract data from the iframe payload
        const {
          access_token,
          id_token,
          refresh_token,
          email: iframeEmail,
          applicationId: iframeAppId,
          applicationStatus,
        } = event.data;

        console.log("Application status from iframe:", applicationStatus);

        // Handle based on application status
        if (applicationStatus === "draft") {
          console.log("Application status is draft, using bypass login");

          const extractedApiKey = decodeAccessToken(access_token);

          // For draft applications, decode access_token and use bypass login
          if (access_token) {
            if (extractedApiKey) {
              console.log(
                "Extracted API key from access token:",
                extractedApiKey
              );
              atomSetters.setApiKey(extractedApiKey);
            } else {
              console.log("Failed to extract API key from access token");
            }
          } else {
            console.log("No access token provided for draft application");
          }

          // Use bypass login for draft applications
          console.log("Calling handleBypassLogin with:", {
            iframeEmail,
            iframeAppId,
          });
          await handleBypassLogin(
            String(iframeEmail),
            String(iframeAppId),
            String(extractedApiKey)
          );
          return;
        } else if (applicationStatus === "new") {
          console.log("Application status is new, setting up Cognito session");

          // For new applications, set up full Cognito session
          if (!access_token || !id_token || !refresh_token) {
            console.error("Missing required tokens for new application setup");
            router.push("/login");
            return;
          }

          try {
            const awsConfig = await getAwsConfiguration();
            const clientId = awsConfig.aws_user_pools_web_client_id;
            const userPoolId = awsConfig.aws_user_pools_id;

            // Get username from the ID token payload
            const payload = JSON.parse(atob(id_token?.split(".")[1]));
            const username = payload.email || payload.sub;

            // Set up the Amplify session with the tokens
            const sessionSetup = await setupAmplifySession(
              username,
              access_token,
              id_token,
              refresh_token,
              userPoolId,
              clientId
            );

            if (sessionSetup) {
              // Store user details
              atomSetters.setUserEmail(String(username));
              atomSetters.setApplication(String(iframeAppId));
              atomSetters.setNextFormDetails(getNextForm());
              atomSetters.setIsAppHero(true);

              // Extract and store API key if available
              const apiKey = decodeAccessToken(access_token);
              if (apiKey) {
                console.log("Extracted API key for new application:", apiKey);
                atomSetters.setApiKey(apiKey);
              }

              // Redirect to application filter page
              router.push("/application-filter");
            } else {
              console.log("Failed to set up session", sessionSetup);
              throw new Error("Failed to set up session");
            }
          } catch (tokenError) {
            console.log("Error setting up Cognito session:", tokenError);
            router.push("/login");
            return;
          }
        } else {
          console.log(
            "Unknown application status or missing tokens, using fallback"
          );

          // Fallback: if no access_token but have email and appId, use bypass
          if (!access_token && iframeEmail && iframeAppId) {
            console.log("No access token provided, using bypass login");
            router.push("/login");
            return;
          } else {
            console.error("Insufficient data for authentication");
            router.push("/login");
            return;
          }
        }
      } else {
        router.push("/login");
        return;
      }

      try {
        isProcessingCode = true;
        // All authentication logic is now handled above in the conditional blocks
        console.log("Authentication processing completed");
      } catch (eventError) {
        console.error("Error during authentication:", eventError);
        router.push("/login");
      } finally {
        isProcessingCode = false;
      }
    };

    // Create a named event handler function for proper cleanup
    const messageEventHandler = (event: any) => {
      // Mark that we've received a message event
      messageReceivedRef.current = true;

      if (event.data?.type === "AUTH_REQUEST") {
        console.log("AUTH_REQUEST detected, calling handleMessageEvent");
        handleMessageEvent(event);
      }
    };

    if (typeof window !== "undefined") {
      window.addEventListener("message", messageEventHandler);
    }

    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("message", messageEventHandler);
      }
    };
  }, [router, isIframe, handleBypassLogin, atomSetters]);

  // Initialize authentication process
  useEffect(() => {
    if (typeof window !== "undefined") {
      const initializeAuth = () => {
        setIsAuthenticating(true);
        setAuthPhase("initializing");
      };

      const checkAuthDirectly = async () => {
        console.log("Checking authentication directly");

        try {
          const { tokens } = await fetchAuthSession();

          if (tokens?.accessToken) {
            // User is already authenticated, redirect immediately
            setAuthPhase("redirecting");
            router.push("application-filter");
          } else {
            // No valid tokens, redirect to login immediately
            setAuthPhase("redirecting");
            router.push("/login");
          }
        } catch (error) {
          console.error("Authentication error:", error);
          setAuthPhase("redirecting");
          router.push("/login");
        }
      };

      const fallbackTimer = () => {
        if (!isIframe && !messageReceivedRef.current) {
          console.log("Fallback timer triggered - no iframe message received");
          checkAuthDirectly();
        }
      };

      // Initialize authentication UI immediately
      initializeAuth();

      // Start authentication process
      if (!isIframe) {
        // For direct access, check auth immediately after initialization
        setTimeout(checkAuthDirectly, 500);
      } else {
        // For iframe, wait for message or fallback after reduced time
        setTimeout(fallbackTimer, 3000);
      }

      return () => {
        // Cleanup if needed
      };
    }
  }, [router, isIframe]);

  const sendTestMessage = () => {
    const testMessage = {
      type: "AUTH_REQUEST",
      email: "<EMAIL>",
      applicationId: "1d5baa3f-f6ba-49a3-9e75-4fb1d0cb6efd",
      brand: "UCW",
      applicationStatus: "draft", // Change this to "new" to test Cognito setup
      access_token:
        "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", // Mock token for testing
      timestamp: new Date().getTime(),
    };

    // Simulate postMessage event
    window.dispatchEvent(
      new MessageEvent("message", {
        data: testMessage,
        origin:
          APPHERO_URL[
            (process.env.NEXT_PUBLIC_NODE_ENV || "dev").toLowerCase()
          ],
      })
    );
  };

  return (
    <main className="min-h-screen bg-primary flex flex-col items-center justify-center overflow-scroll">
      <div className="w-full max-w-lg px-6">
        {isAuthenticating ? (
          <DynamicAuthProgress isIframe={isIframe} currentPhase={authPhase} />
        ) : (
          <div className="text-white text-center">
            <h1 className="text-2xl mb-4">Initializing...</h1>
            <p>
              {isIframe
                ? "Preparing to receive authentication data..."
                : "Setting up authentication session..."}
            </p>
          </div>
        )}

        {/* Test Button for Development */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 text-center">
            <button
              onClick={sendTestMessage}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium"
            >
              🧪 Test Bypass Auth Message
            </button>
          </div>
        )}

        <div className="mt-8 text-center">
          <p className="text-sm text-white/70">
            If you&apos;re not redirected automatically,{" "}
            <button
              onClick={() => router.push("/login")}
              className="underline text-blue-300 hover:text-blue-100"
            >
              click here to login
            </button>
          </p>
        </div>
      </div>
    </main>
  );
}
